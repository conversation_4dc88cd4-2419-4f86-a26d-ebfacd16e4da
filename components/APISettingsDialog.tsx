"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Settings, Eye, EyeOff } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

interface APISettings {
  apiKey: string;
  apiBase: string;
  modelName: string;
}

interface APISettingsDialogProps {
  onSettingsChange: (settings: APISettings) => void;
}

export default function APISettingsDialog({
  onSettingsChange,
}: APISettingsDialogProps) {
  const t = useTranslations("APISettings");
  const [open, setOpen] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [settings, setSettings] = useState<APISettings>({
    apiKey: "",
    apiBase: "https://api.openai.com/v1",
    modelName: "",
  });
  const [models, setModels] = useState<string[]>([]);
  const [loadingModels, setLoadingModels] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem("codeocr-api-settings");
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(parsed);
        onSettingsChange(parsed);
      } catch (error) {
        console.error("Failed to parse saved settings:", error);
      }
    }
  }, [onSettingsChange]);

  // Load models when dialog opens
  const loadModels = useCallback(async () => {
    setLoadingModels(true);
    try {
      const response = await fetch("/api/v1/models");
      if (response.ok) {
        const data = await response.json();
        setModels(data.models || []);
      } else {
        toast.error(t("failedToLoadModels"));
      }
    } catch (error) {
      console.error("Failed to load models:", error);
      toast.error(t("failedToLoadModels"));
    } finally {
      setLoadingModels(false);
    }
  }, [t]);

  useEffect(() => {
    if (open) {
      loadModels();
    }
  }, [open, loadModels]);

  const handleSave = () => {
    try {
      localStorage.setItem("codeocr-api-settings", JSON.stringify(settings));
      onSettingsChange(settings);
      toast.success(t("settingsSaved"));
      setOpen(false);
    } catch (error) {
      console.error("Failed to save settings:", error);
      toast.error(t("settingsFailed"));
    }
  };

  const handleInputChange = (field: keyof APISettings, value: string) => {
    setSettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="lg">
          <Settings className="mr-2 h-4 w-4" />
          {t("title")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="apiKey">{t("apiKey")}</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                placeholder={t("apiKeyPlaceholder")}
                value={settings.apiKey}
                onChange={(e) => handleInputChange("apiKey", e.target.value)}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="apiBase">{t("apiBase")}</Label>
            <Input
              id="apiBase"
              placeholder={t("apiBasePlaceholder")}
              value={settings.apiBase}
              onChange={(e) => handleInputChange("apiBase", e.target.value)}
            />
            <p className="text-sm text-muted-foreground">{t("apiBaseHint")}</p>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="modelName">{t("modelName")}</Label>
            <Select
              value={settings.modelName}
              onValueChange={(value) => handleInputChange("modelName", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t("modelNamePlaceholder")} />
              </SelectTrigger>
              <SelectContent>
                {loadingModels ? (
                  <SelectItem value="loading" disabled>
                    {t("loadingModels")}
                  </SelectItem>
                ) : models.length > 0 ? (
                  models.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="error" disabled>
                    {t("failedToLoadModels")}
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>{t("saveChanges")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
